# Blender AI Agent System - <PERSON><PERSON>

## 📋 Genel Bakış
Blender 4.4+ için geliştirilmiş kapsamlı bir AI agent sistemi. Crew AI framework'ü ile Model Context Protocol (MCP) sunucularını entegre ederek, otomatik 3D iş akışları için kullanıcı dostu bir arayüz sağlar.

## 🎯 Ana Hedef
Blender kullanıcılarının doğal dil ile AI ajanlarıyla konuşarak karmaşık 3D operasyonları gerçekleştirmesini sağlamak.

## 🚀 Temel Özellikler

### 💬 Chat-First AI Arayüzü
- **Doğal Dil Kontrolü**: Türkçe/İngilizce ile Blender'ı kontrol etme
- **Bağlam Farkında Asistan**: Mevcut sahne ve seçimleri anlayan AI
- **Akıllı Öneriler**: İş akışına dayalı zeki öneriler
- **Tek Tıkla E<PERSON>mler**: <PERSON><PERSON><PERSON> komutlarla karmaşık operasyonlar

### 🤖 AI Agent Sistemi
- **7 Uzman Agent Türü**: 
  - 3D Modelleme Uzmanı
  - Materyal ve Tekstür Sanatçısı
  - Animasyon Uzmanı
  - Aydınlatma Sanatçısı
  - Render Uzmanı
  - Sahne Yöneticisi
  - Genel Asistan
- **Çoklu-Agent Ekipleri**: Birlikte çalışan agent takımları
- **Şablon Sistemi**: Yaygın görevler için önceden yapılandırılmış ajanlar
- **Arka Plan İşleme**: İlerleme takibi ile engelleyici olmayan görev yürütme

### 🛠️ Blender Entegrasyonu
- **30+ Blender Aracı**: Mesh manipülasyonu, materyaller, aydınlatma, animasyon, render
- **Sahne Yönetimi**: Koleksiyon organizasyonu, temizlik, optimizasyon
- **Gerçek Zamanlı İzleme**: Ajanlar, ekipler ve görevlerin canlı durumu
- **Yerel UI**: Blender'ın 3D Viewport'una entegre paneller

### 🔌 MCP Entegrasyonu
- **Sunucu Yönetimi**: Harici MCP sunucularına bağlanma (dosya sistemi, web, veritabanı)
- **Araç Kayıt Defteri**: Birden fazla kaynaktan 50+ araca birleşik erişim
- **Bağlam Paylaşımı**: Harici araçlar için gerçek zamanlı Blender bağlamı
- **Sunucu Şablonları**: Yaygın kullanım durumları için önceden yapılandırılmış sunucular

### 🎨 Kullanıcı Arayüzü
- **Profesyonel İkon Sistemi**: Tam Blender-yerel ikon entegrasyonu
- **Bağlam Farkında UI**: Araç kategorilerine dayalı dinamik ikonlar
- **Tutarlı Görsel Dil**: Tüm panellerde birleşik tasarım
- **Sezgisel Navigasyon**: Net görsel hiyerarşi ve durum göstergeleri

## 🏗️ Teknik Mimari

### Ana Bileşenler
```
blender-ai-agent/
├── __init__.py                    # Ana addon giriş noktası
├── preferences.py                 # Kullanıcı tercihleri ve ayarları
├── core/                          # Çekirdek sistem bileşenleri
│   ├── addon_manager.py           # Addon yaşam döngüsü yönetimi
│   ├── dependency_manager.py      # Paket yönetimi
│   └── config_manager.py          # Yapılandırma işleme
├── chat_system/                   # Sohbet sistemi
│   ├── chat_interface.py          # Ana sohbet arayüzü
│   ├── nlp_processor.py           # Türkçe NLP işlemcisi
│   ├── llm_nlp_processor.py       # LLM tabanlı NLP
│   ├── agent_bridge.py            # Agent entegrasyonu
│   └── suggestions.py             # Akıllı öneriler
├── crew_system/                   # Crew AI entegrasyonu
│   ├── crew_manager.py            # Crew yönetimi
│   ├── agent_factory.py           # Agent oluşturma
│   ├── blender_tools.py           # Blender araçları
│   └── task_executor.py           # Görev yürütücü
├── mcp_integration/               # MCP entegrasyonu
│   ├── mcp_client.py              # MCP istemcisi
│   ├── server_manager.py          # Sunucu yönetimi
│   ├── tool_registry.py           # Araç kayıt defteri
│   └── context_provider.py        # Bağlam sağlayıcı
├── ui/                            # Kullanıcı arayüzü
│   ├── main_panel.py              # Ana kontrol paneli
│   ├── chat_panel.py              # Sohbet paneli
│   ├── agent_panel.py             # Agent yönetim paneli
│   └── mcp_panel.py               # MCP yönetim paneli
└── operators/                     # Blender operatörleri
    ├── chat_operators.py          # Sohbet operatörleri
    ├── agent_operators.py         # Agent operatörleri
    ├── mcp_operators.py           # MCP operatörleri
    └── system_operators.py        # Sistem operatörleri
```

### Kullanılan Teknolojiler
- **Crew AI Framework v0.141.0**: Çoklu-agent orkestrasyon platformu
- **Model Context Protocol (MCP) v1.11.0**: Harici araç entegrasyonu
- **LiteLLM v1.72.6**: LLM sağlayıcı entegrasyonu
- **OpenAI API**: AI model desteği
- **Blender Python API**: Blender entegrasyonu

## 🔧 Özellik Detayları

### Doğal Dil İşleme (NLP)
- **Türkçe Intent Tanıma**: Gelişmiş Türkçe niyet tanıma sistemi
- **Bağlam Anlama**: Varlık çıkarma ve çok turlu konuşma desteği
- **Akıllı Yanıt Üretimi**: Bağlama uygun yanıt şablonları
- **Intent Kategorileri**:
  - Obje Oluşturma (CREATE_OBJECT)
  - Obje Değiştirme (MODIFY_OBJECT)
  - Animasyon (ANIMATE)
  - Materyal (MATERIAL)
  - Aydınlatma (LIGHTING)
  - Render (RENDER)
  - Sahne Yönetimi (SCENE_MANAGEMENT)
  - Yardım (HELP)

### Blender Araçları (30+ Araç)
#### Mesh Manipülasyon
- Primitif mesh oluşturma (küp, küre, silindir, düzlem, maymun)
- Modifier uygulama (subdivision, mirror, bevel)
- Mesh düzenleme ve optimizasyon

#### Materyal ve Tekstür
- PBR materyal oluşturma
- Tekstür uygulama
- Shader düğüm ağları

#### Aydınlatma
- Işık ekleme ve yapılandırma
- HDRI ortam aydınlatması
- Gölge ve parlaklık ayarları

#### Animasyon
- Keyframe oluşturma
- Rigging araçları
- Hareket yolları

#### Render
- Render ayarları yapılandırma
- Görüntü çıktısı
- Video render

#### Sahne Yönetimi
- Koleksiyon organizasyonu
- Obje temizleme
- Sahne optimizasyonu

### MCP Sunucu Şablonları
- **Dosya Sistemi Sunucusu**: Yerel dosya sistemi erişimi
- **Web Arama Sunucusu**: İnternet arama yetenekleri
- **Veritabanı Sunucusu**: SQL veritabanı işlemleri
- **Git Sunucusu**: Versiyon kontrol entegrasyonu
- **Blender Araçları Sunucusu**: Genişletilmiş 3D işlemler

## 📊 Geliştirme Durumu

### Tamamlanan Fazlar
- ✅ **Faz 1**: Temel & Altyapı
- ✅ **Faz 2**: Crew AI Entegrasyonu
- ✅ **Faz 3**: MCP Entegrasyonu
- ✅ **Faz 4A**: Chat-First Arayüz

### Devam Eden/Planlanan
- 🔄 **Faz 4B**: Gelişmiş UI Bileşenleri
- ⏳ **Faz 5**: Test & Cilalama

## 🎮 Kullanım Senaryoları

### Başlangıç Kullanıcıları
- "Bir ev modeli yap"
- "Kırmızı materyal ekle"
- "Sahneyi aydınlat"

### İleri Düzey Kullanıcılar
- Karmaşık çoklu-agent iş akışları
- Özel MCP sunucu entegrasyonları
- Otomatik render pipeline'ları

## 🔒 Güvenlik ve Performans
- Güvenli dependency yönetimi
- Hata yakalama ve kurtarma
- Kaynak temizleme
- Performans izleme

## 📈 Gelecek Planları
- Daha fazla AI model desteği
- Gelişmiş animasyon araçları
- Topluluk MCP sunucu pazarı
- Çoklu dil desteği genişletme

## 👨‍💻 Geliştirici Bilgileri
- **Yazar**: inkbytefo
- **Lisans**: MIT License
- **Blender Uyumluluğu**: 4.4.0+
- **Python Gereksinimleri**: 3.8+

## 🔗 Bağlantılar
- GitHub Repository: [blender-ai-agent](https://github.com/inkbytefo/blender-ai-agent)
- Dokümantasyon: docs/user_guide.md
- Issue Tracker: GitHub Issues
