"""
Addon Manager - Core addon lifecycle management
"""

import bpy
import logging
from pathlib import Path

# Handle imports with fallback for Blender addon context
try:
    from .dependency_manager import get_dependency_manager
    from ..utils.singleton import Singleton
    from ..utils.common_utils import get_logger
except ImportError:
    # Fallback for when running as Blender addon
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from core.dependency_manager import get_dependency_manager
    from utils.singleton import Singleton
    from utils.common_utils import get_logger

logger = get_logger("addon_manager")

class AddonManager(Singleton):
    """Manages addon lifecycle, dependencies, and core functionality"""
    
    def __init__(self):
        self.addon_dir = Path(__file__).parent.parent
        self.is_initialized = False
        self.dependencies_installed = False
        
    def initialize(self):
        """Initialize the addon system"""
        try:
            logger.info("Initializing AI Agent System...")
            
            # Check Blender version compatibility
            if not self._check_blender_compatibility():
                raise RuntimeError("Blender version not compatible")
            
            # Setup addon directories
            self._setup_directories()
            
            # Check dependencies
            self._check_dependencies()
            
            self.is_initialized = True
            logger.info("AI Agent System initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize addon: {e}")
            raise
    
    def cleanup(self):
        """Cleanup addon resources"""
        try:
            logger.info("Cleaning up AI Agent System...")
            
            # Stop any running processes
            self._stop_background_processes()
            
            # Clear caches
            self._clear_caches()
            
            self.is_initialized = False
            logger.info("AI Agent System cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Failed to cleanup addon: {e}")
    
    def _check_blender_compatibility(self):
        """Check if current Blender version is compatible"""
        version = bpy.app.version
        min_version = (4, 4, 0)
        
        if version < min_version:
            logger.error(f"Blender {version} is not compatible. Minimum required: {min_version}")
            return False
        
        logger.info(f"Blender {version} is compatible")
        return True
    
    def _setup_directories(self):
        """Setup required directories"""
        directories = [
            self.addon_dir / "temp",
            self.addon_dir / "logs",
            self.addon_dir / "cache",
            self.addon_dir / "configs"
        ]
        
        for directory in directories:
            directory.mkdir(exist_ok=True)
            logger.debug(f"Created directory: {directory}")
    
    def _check_dependencies(self):
        """Check if required dependencies are available"""
        dependency_manager = get_dependency_manager()

        # Check basic Python modules first
        required_modules = [
            "json",
            "asyncio",
            "threading",
            "urllib.request"
        ]

        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)

        if missing_modules:
            logger.warning(f"Missing basic modules: {missing_modules}")
            self.dependencies_installed = False
            return

        # Check addon-specific dependencies
        self.dependencies_installed = dependency_manager.check_dependencies()

        if self.dependencies_installed:
            logger.info("All dependencies available")
        else:
            logger.warning("Some addon dependencies are missing")
    
    def _stop_background_processes(self):
        """Stop any running background processes"""
        try:
            # Stop MCP client background tasks
            from ..mcp_integration import mcp_client
            if hasattr(mcp_client, 'get_mcp_client'):
                client = mcp_client.get_mcp_client()
                if hasattr(client, 'stop'):
                    client.stop()

            # Stop server manager health monitoring
            from ..mcp_integration import server_manager
            if hasattr(server_manager, 'get_server_manager'):
                manager = server_manager.get_server_manager()
                if hasattr(manager, 'stop_health_monitoring'):
                    manager.stop_health_monitoring()

            # Stop any chat system background tasks
            from ..chat_system import chat_interface
            if hasattr(chat_interface, 'get_chat_interface'):
                interface = chat_interface.get_chat_interface()
                if hasattr(interface, 'cleanup'):
                    interface.cleanup()

            logger.info("Background processes stopped successfully")

        except Exception as e:
            logger.warning(f"Error stopping background processes: {e}")
    
    def _clear_caches(self):
        """Clear addon caches"""
        cache_dir = self.addon_dir / "cache"
        if cache_dir.exists():
            for file in cache_dir.glob("*"):
                try:
                    file.unlink()
                except Exception as e:
                    logger.warning(f"Failed to clear cache file {file}: {e}")

def initialize():
    """Initialize the addon manager"""
    get_manager().initialize()

def cleanup():
    """Cleanup the addon manager"""
    get_manager().cleanup()

def get_manager():
    """Get the addon manager instance"""
    return AddonManager.get_instance()
