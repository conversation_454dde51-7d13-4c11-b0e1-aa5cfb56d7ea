try:
    from llm_integration import LLMConfig, LLMProviderType, get_llm_provider, list_available_providers
    from utils.logging_config import get_logger
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from llm_integration import LLMConfig, LLMProviderType, get_llm_provider, list_available_providers
    from utils.logging_config import get_logger

"""
LLM Configuration System - User-configurable LLM settings
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Import bpy only when available (in Blender context)
try:
    import bpy
    BPY_AVAILABLE = True
except ImportError:
    BPY_AVAILABLE = False




logger = get_logger("llm_config")

@dataclass
class LLMPreset:
    """Predefined LLM configuration preset"""
    name: str
    description: str
    provider_type: LLMProviderType
    model: str
    temperature: float
    max_tokens: int
    use_case: str  # "general", "creative", "technical", "fast"
    base_url: Optional[str] = None

    def to_llm_config(self, api_key: Optional[str] = None, base_url: Optional[str] = None) -> LLMConfig:
        """Convert preset to LLMConfig"""
        return LLMConfig(
            provider_type=self.provider_type,
            model=self.model,
            api_key=api_key,
            base_url=base_url or self.base_url,
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )

class LLMConfigManager:
    """Manages LLM configuration and user preferences"""
    
    def __init__(self):
        self.config_file = self._get_config_file_path()
        self.current_config: Optional[LLMConfig] = None
        self.user_preferences: Dict[str, Any] = {}
        self.presets: Dict[str, LLMPreset] = {}
        self._load_default_presets()
        
    def _get_config_file_path(self) -> Path:
        """Get configuration file path"""
        # Use Blender's user config directory
        config_dir = Path(bpy.utils.user_resource('CONFIG'))
        addon_config_dir = config_dir / "blender_ai_agent"
        addon_config_dir.mkdir(exist_ok=True)
        return addon_config_dir / "llm_config.json"
    
    def _load_default_presets(self):
        """Load default LLM presets"""
        self.presets = {
            "fast_general": LLMPreset(
                name="Hızlı Genel",
                description="Hızlı yanıt için optimize edilmiş genel kullanım",
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4o-mini",
                temperature=0.7,
                max_tokens=1500,
                use_case="fast"
            ),
            "balanced": LLMPreset(
                name="Dengeli",
                description="Hız ve kalite arasında denge",
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4o",
                temperature=0.7,
                max_tokens=2000,
                use_case="general"
            ),
            "creative": LLMPreset(
                name="Yaratıcı",
                description="Yaratıcı projeler için yüksek sıcaklık",
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4o",
                temperature=0.9,
                max_tokens=2500,
                use_case="creative"
            ),
            "technical": LLMPreset(
                name="Teknik",
                description="Teknik işlemler için düşük sıcaklık",
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4o",
                temperature=0.3,
                max_tokens=2000,
                use_case="technical"
            ),
            "premium": LLMPreset(
                name="Premium",
                description="En yüksek kalite için GPT-4",
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4",
                temperature=0.7,
                max_tokens=3000,
                use_case="general"
            ),
            "custom_openai": LLMPreset(
                name="Özel OpenAI Uyumlu",
                description="Özel OpenAI uyumlu API endpoint'i",
                provider_type=LLMProviderType.OPENAI_COMPATIBLE,
                model="gpt-4o-mini",
                temperature=0.7,
                max_tokens=2000,
                use_case="general",
                base_url="https://api.openai.com/v1"
            ),
            "groq_fast": LLMPreset(
                name="Groq Hızlı",
                description="Groq ile hızlı yanıt",
                provider_type=LLMProviderType.OPENAI_COMPATIBLE,
                model="llama-3.1-8b-instant",
                temperature=0.7,
                max_tokens=2000,
                use_case="fast",
                base_url="https://api.groq.com/openai/v1"
            ),
            "anthropic_claude": LLMPreset(
                name="Claude (Uyumlu)",
                description="Claude modeli OpenAI uyumlu API ile",
                provider_type=LLMProviderType.OPENAI_COMPATIBLE,
                model="claude-3-5-sonnet-20241022",
                temperature=0.7,
                max_tokens=2000,
                use_case="general",
                base_url="https://api.anthropic.com/v1"
            )
        }
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if not self.config_file.exists():
                logger.info("Config file not found, using defaults")
                return self._create_default_config()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Load current config
            if 'current_config' in data:
                config_data = data['current_config']
                self.current_config = LLMConfig(
                    provider_type=LLMProviderType(config_data['provider_type']),
                    model=config_data['model'],
                    api_key=config_data.get('api_key'),
                    temperature=config_data.get('temperature', 0.7),
                    max_tokens=config_data.get('max_tokens', 2000),
                    timeout=config_data.get('timeout', 30),
                    extra_params=config_data.get('extra_params', {})
                )
            
            # Load user preferences
            self.user_preferences = data.get('user_preferences', {})
            
            logger.info("LLM configuration loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load LLM config: {e}")
            return self._create_default_config()
    
    def save_config(self) -> bool:
        """Save configuration to file"""
        try:
            data = {
                'current_config': asdict(self.current_config) if self.current_config else None,
                'user_preferences': self.user_preferences,
                'version': '1.0'
            }
            
            # Convert enum to string for JSON serialization
            if data['current_config']:
                data['current_config']['provider_type'] = data['current_config']['provider_type'].value
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info("LLM configuration saved successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save LLM config: {e}")
            return False
    
    def _create_default_config(self) -> bool:
        """Create default configuration"""
        try:
            # Use balanced preset as default
            preset = self.presets["balanced"]
            self.current_config = preset.to_llm_config()
            
            # Default user preferences
            self.user_preferences = {
                'language': 'turkish',
                'response_style': 'friendly',
                'show_reasoning': True,
                'auto_save_conversations': True,
                'max_conversation_history': 10
            }
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"Failed to create default config: {e}")
            return False
    
    def set_preset(self, preset_name: str, api_key: Optional[str] = None) -> bool:
        """Set configuration from preset"""
        try:
            if preset_name not in self.presets:
                logger.error(f"Unknown preset: {preset_name}")
                return False
            
            preset = self.presets[preset_name]
            self.current_config = preset.to_llm_config(api_key)
            
            # Update user preferences
            self.user_preferences['current_preset'] = preset_name
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"Failed to set preset: {e}")
            return False
    
    def set_custom_config(
        self,
        provider_type: LLMProviderType,
        model: str,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Set custom configuration"""
        try:
            self.current_config = LLMConfig(
                provider_type=provider_type,
                model=model,
                api_key=api_key,
                base_url=base_url,
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 2000),
                timeout=kwargs.get('timeout', 30),
                extra_params=kwargs.get('extra_params', {})
            )
            
            # Mark as custom config
            self.user_preferences['current_preset'] = 'custom'
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"Failed to set custom config: {e}")
            return False
    
    def get_current_config(self) -> Optional[LLMConfig]:
        """Get current LLM configuration"""
        return self.current_config
    
    def get_presets(self) -> Dict[str, LLMPreset]:
        """Get available presets"""
        return self.presets.copy()
    
    def get_available_providers(self) -> List[LLMProviderType]:
        """Get available LLM providers"""
        return list_available_providers()
    
    def validate_config(self, config: Optional[LLMConfig] = None) -> Tuple[bool, str]:
        """Validate LLM configuration"""
        try:
            config = config or self.current_config
            if not config:
                return False, "No configuration set"
            
            # Check if provider is supported
            if config.provider_type not in self.get_available_providers():
                return False, f"Provider {config.provider_type.value} not supported"
            
            # Check API key for providers that require it
            if config.provider_type == LLMProviderType.OPENAI and not config.api_key:
                return False, "OpenAI API key required"

            if config.provider_type == LLMProviderType.OPENAI_COMPATIBLE:
                if not config.api_key:
                    return False, "API key required for OpenAI Compatible provider"
                if not config.base_url:
                    return False, "Base URL required for OpenAI Compatible provider"
            
            # Validate model
            provider = get_llm_provider(config)
            if provider:
                available_models = provider.get_available_models()
                if config.model not in available_models:
                    return False, f"Model {config.model} not available for {config.provider_type.value}"
            
            return True, "Configuration is valid"
            
        except Exception as e:
            return False, f"Validation error: {e}"
    
    def test_connection(self, config: Optional[LLMConfig] = None) -> Tuple[bool, str]:
        """Test LLM connection"""
        try:
            config = config or self.current_config
            if not config:
                return False, "No configuration set"
            
            # Validate first
            is_valid, message = self.validate_config(config)
            if not is_valid:
                return False, message
            
            # Create provider and test
            provider = get_llm_provider(config)
            if not provider:
                return False, "Failed to create provider"
            
            # This would need to be async in real implementation
            # For now, just validate the config
            return True, "Connection test passed (validation only)"
            
        except Exception as e:
            return False, f"Connection test failed: {e}"
    
    def get_user_preference(self, key: str, default: Any = None) -> Any:
        """Get user preference"""
        return self.user_preferences.get(key, default)
    
    def set_user_preference(self, key: str, value: Any) -> bool:
        """Set user preference"""
        try:
            self.user_preferences[key] = value
            return self.save_config()
        except Exception as e:
            logger.error(f"Failed to set user preference: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for UI"""
        if not self.current_config:
            return {"status": "not_configured"}
        
        is_valid, validation_message = self.validate_config()
        
        return {
            "status": "configured",
            "provider": self.current_config.provider_type.value,
            "model": self.current_config.model,
            "has_api_key": bool(self.current_config.api_key),
            "base_url": self.current_config.base_url,
            "temperature": self.current_config.temperature,
            "max_tokens": self.current_config.max_tokens,
            "is_valid": is_valid,
            "validation_message": validation_message,
            "current_preset": self.user_preferences.get('current_preset', 'custom')
        }
    
    def reset_to_defaults(self) -> bool:
        """Reset configuration to defaults"""
        try:
            self.current_config = None
            self.user_preferences = {}
            return self._create_default_config()
        except Exception as e:
            logger.error(f"Failed to reset config: {e}")
            return False

# Global config manager instance
_config_manager = None

def get_llm_config_manager() -> LLMConfigManager:
    """Get the LLM config manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = LLMConfigManager()
        _config_manager.load_config()
    return _config_manager
