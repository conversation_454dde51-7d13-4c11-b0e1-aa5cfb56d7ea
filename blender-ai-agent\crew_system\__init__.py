"""
Crew System Module - Crew AI integration for Blender
"""

try:
    from . import crew_manager
    from . import agent_factory
    from . import task_executor
    from . import blender_tools
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from crew_system import crew_manager
    from crew_system import agent_factory
    from crew_system import task_executor
    from crew_system import blender_tools

# List of modules to register
modules = [
    crew_manager,
    agent_factory,
    task_executor,
    blender_tools,
]

def register():
    """Register all crew system components"""
    for module in modules:
        if hasattr(module, 'register'):
            module.register()

def unregister():
    """Unregister all crew system components"""
    for module in reversed(modules):
        if hasattr(module, 'unregister'):
            module.unregister()
