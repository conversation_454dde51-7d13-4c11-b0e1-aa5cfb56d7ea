try:
    from mcp_integration.context_provider import get_context_provider
    from utils.logging_config import get_logger
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from mcp_integration.context_provider import get_context_provider
    from utils.logging_config import get_logger

"""
Smart Suggestions System - Context-aware quick actions and recommendations
"""

from typing import List, Dict, Optional, Any
from dataclasses import dataclass

# Import bpy only when available (in Blender context)
try:
    import bpy
    BPY_AVAILABLE = True
except ImportError:
    BPY_AVAILABLE = False




logger = get_logger("suggestions")

@dataclass
class Suggestion:
    """Smart suggestion for user actions"""
    id: str
    text: str
    description: str
    icon: str
    category: str
    priority: int = 1
    context_requirements: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context_requirements is None:
            self.context_requirements = {}

class SuggestionsSystem:
    """Context-aware suggestions system"""
    
    def __init__(self):
        self.context_provider = get_context_provider()
        self.base_suggestions = []
        self.context_suggestions = []
        self.is_initialized = False
        
    def initialize(self):
        """Initialize suggestions system"""
        try:
            self._load_base_suggestions()
            self.is_initialized = True
            logger.info("Suggestions system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize suggestions system: {e}")
            raise
    
    def _load_base_suggestions(self):
        """Load base suggestions that are always available"""
        self.base_suggestions = [
            # Object Creation
            Suggestion(
                id="create_cube",
                text="Küp oluştur",
                description="Sahneye yeni bir küp ekle",
                icon="MESH_CUBE",
                category="create",
                priority=5
            ),
            
            Suggestion(
                id="create_sphere",
                text="Küre oluştur", 
                description="Sahneye yeni bir küre ekle",
                icon="MESH_UVSPHERE",
                category="create",
                priority=5
            ),
            
            Suggestion(
                id="create_cylinder",
                text="Silindir oluştur",
                description="Sahneye yeni bir silindir ekle", 
                icon="MESH_CYLINDER",
                category="create",
                priority=4
            ),
            
            # Lighting
            Suggestion(
                id="add_sun_light",
                text="Güneş ışığı ekle",
                description="Sahneye güneş ışığı ekle",
                icon="LIGHT_SUN",
                category="lighting",
                priority=4
            ),
            
            Suggestion(
                id="add_area_light",
                text="Alan ışığı ekle",
                description="Sahneye yumuşak alan ışığı ekle",
                icon="LIGHT_AREA",
                category="lighting", 
                priority=3
            ),
            
            # Materials
            Suggestion(
                id="create_material",
                text="Materyal oluştur",
                description="Yeni PBR materyal oluştur",
                icon="MATERIAL",
                category="material",
                priority=3
            ),
            
            # Scene Management
            Suggestion(
                id="organize_scene",
                text="Sahneyi düzenle",
                description="Objeleri koleksiyonlarda organize et",
                icon="OUTLINER",
                category="scene",
                priority=2
            ),
            
            # Render
            Suggestion(
                id="setup_render",
                text="Render ayarla",
                description="Render ayarlarını optimize et",
                icon="RENDER_STILL",
                category="render",
                priority=2
            )
        ]
    
    def get_suggestions(self, max_count: int = 6) -> List[Suggestion]:
        """Get context-aware suggestions"""
        try:
            # Get current context
            context = self._get_current_context()
            
            # Generate context-specific suggestions
            context_suggestions = self._generate_context_suggestions(context)
            
            # Combine and prioritize suggestions
            all_suggestions = context_suggestions + self.base_suggestions
            
            # Sort by priority (higher first) and limit count
            sorted_suggestions = sorted(all_suggestions, key=lambda x: x.priority, reverse=True)
            
            return sorted_suggestions[:max_count]
            
        except Exception as e:
            logger.error(f"Failed to get suggestions: {e}")
            return self.base_suggestions[:max_count]
    
    def _get_current_context(self) -> Dict[str, Any]:
        """Get current Blender context"""
        try:
            context_summary = self.context_provider.get_context_summary()

            # Get selected objects info
            selected_objects = []
            if BPY_AVAILABLE and bpy.context.selected_objects:
                for obj in bpy.context.selected_objects:
                    selected_objects.append({
                        'name': obj.name,
                        'type': obj.type,
                        'has_material': len(obj.material_slots) > 0
                    })

            return {
                'scene_name': context_summary.get('scene', {}).get('name', ''),
                'total_objects': context_summary.get('statistics', {}).get('total_objects', 0),
                'selected_objects': selected_objects,
                'active_object': bpy.context.active_object.name if (BPY_AVAILABLE and bpy.context.active_object) else None,
                'mode': bpy.context.mode if BPY_AVAILABLE else 'OBJECT',
                'has_lights': context_summary.get('statistics', {}).get('lights', 0) > 0,
                'has_camera': context_summary.get('statistics', {}).get('cameras', 0) > 0
            }
            
        except Exception as e:
            logger.warning(f"Failed to get context: {e}")
            return {}
    
    def _generate_context_suggestions(self, context: Dict[str, Any]) -> List[Suggestion]:
        """Generate suggestions based on current context"""
        suggestions = []
        
        try:
            selected_objects = context.get('selected_objects', [])
            active_object = context.get('active_object')
            mode = context.get('mode', 'OBJECT')
            has_lights = context.get('has_lights', False)
            has_camera = context.get('has_camera', False)
            total_objects = context.get('total_objects', 0)
            
            # Object-specific suggestions
            if active_object:
                # Material suggestions for objects without materials
                selected_without_material = [obj for obj in selected_objects if not obj['has_material']]
                if selected_without_material:
                    suggestions.append(Suggestion(
                        id="add_material_to_selected",
                        text=f"{active_object} için materyal oluştur",
                        description="Seçili obje için PBR materyal oluştur",
                        icon="MATERIAL_DATA",
                        category="material",
                        priority=8
                    ))
                
                # Animation suggestions for mesh objects
                mesh_objects = [obj for obj in selected_objects if obj['type'] == 'MESH']
                if mesh_objects:
                    suggestions.append(Suggestion(
                        id="animate_selected",
                        text=f"{active_object} animasyonlu yap",
                        description="Seçili obje için basit animasyon oluştur",
                        icon="ARMATURE_DATA",
                        category="animation",
                        priority=6
                    ))
                
                # Modifier suggestions for mesh objects
                if mesh_objects:
                    suggestions.append(Suggestion(
                        id="add_subdivision",
                        text=f"{active_object} yumuşat",
                        description="Subdivision modifier ekle",
                        icon="MOD_SUBSURF",
                        category="modify",
                        priority=5
                    ))
            
            # Scene-level suggestions
            if not has_lights:
                suggestions.append(Suggestion(
                    id="add_basic_lighting",
                    text="Temel aydınlatma kur",
                    description="Sahne için 3-nokta aydınlatma sistemi kur",
                    icon="LIGHT_SUN",
                    category="lighting",
                    priority=9
                ))
            
            if not has_camera:
                suggestions.append(Suggestion(
                    id="add_camera",
                    text="Kamera ekle",
                    description="Sahneye render kamerası ekle",
                    icon="OUTLINER_OB_CAMERA",
                    category="scene",
                    priority=7
                ))
            
            # Mode-specific suggestions
            if mode == 'EDIT':
                suggestions.append(Suggestion(
                    id="extrude_faces",
                    text="Yüzeyleri çıkart",
                    description="Seçili yüzeyleri extrude et",
                    icon="MESH_DATA",
                    category="edit",
                    priority=7
                ))
            
            # Project completion suggestions
            if total_objects > 3 and has_lights and has_camera:
                suggestions.append(Suggestion(
                    id="render_preview",
                    text="Önizleme render al",
                    description="Hızlı önizleme render oluştur",
                    icon="RENDER_STILL",
                    category="render",
                    priority=6
                ))
            
            # Workflow suggestions
            if total_objects > 5:
                suggestions.append(Suggestion(
                    id="organize_collections",
                    text="Koleksiyonlarda organize et",
                    description="Objeleri kategorilere göre organize et",
                    icon="OUTLINER_COLLECTION",
                    category="scene",
                    priority=4
                ))
            
        except Exception as e:
            logger.error(f"Failed to generate context suggestions: {e}")
        
        return suggestions
    
    def get_suggestions_by_category(self, category: str) -> List[Suggestion]:
        """Get suggestions filtered by category"""
        all_suggestions = self.get_suggestions(max_count=20)
        return [s for s in all_suggestions if s.category == category]
    
    def get_suggestion_by_id(self, suggestion_id: str) -> Optional[Suggestion]:
        """Get specific suggestion by ID"""
        all_suggestions = self.get_suggestions(max_count=50)
        for suggestion in all_suggestions:
            if suggestion.id == suggestion_id:
                return suggestion
        return None
    
    def execute_suggestion(self, suggestion_id: str) -> Dict[str, Any]:
        """Execute a suggestion and return result"""
        try:
            suggestion = self.get_suggestion_by_id(suggestion_id)
            if not suggestion:
                return {'success': False, 'error': 'Suggestion not found'}
            
            # Map suggestion IDs to chat commands
            command_map = {
                'create_cube': 'Küp oluştur',
                'create_sphere': 'Küre oluştur', 
                'create_cylinder': 'Silindir oluştur',
                'add_sun_light': 'Güneş ışığı ekle',
                'add_area_light': 'Alan ışığı ekle',
                'create_material': 'Materyal oluştur',
                'add_material_to_selected': 'Bu obje için materyal oluştur',
                'animate_selected': 'Bu objeyi animasyonlu yap',
                'add_subdivision': 'Bu objeyi yumuşat',
                'add_basic_lighting': 'Sahneyi aydınlat',
                'add_camera': 'Kamera ekle',
                'render_preview': 'Önizleme render al',
                'organize_collections': 'Sahneyi organize et'
            }
            
            command = command_map.get(suggestion_id, suggestion.text)
            
            return {
                'success': True,
                'command': command,
                'suggestion': suggestion
            }
            
        except Exception as e:
            logger.error(f"Failed to execute suggestion {suggestion_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def cleanup(self):
        """Cleanup suggestions system"""
        self.base_suggestions.clear()
        self.context_suggestions.clear()
        self.is_initialized = False
        logger.info("Suggestions system cleaned up")

# Global suggestions system instance
_suggestions_system = None

def get_suggestions_system():
    """Get the suggestions system instance"""
    global _suggestions_system
    if _suggestions_system is None:
        _suggestions_system = SuggestionsSystem()
    return _suggestions_system
